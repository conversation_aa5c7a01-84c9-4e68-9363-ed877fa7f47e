<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>VLESS 代理配置面板 - UI测试</title>
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
<style>
:root {
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --secondary-color: #3b82f6;
  --accent-color: #06b6d4;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --dark-color: #1f2937;
  --text-color: #374151;
  --text-light: #6b7280;
  --light-color: #f9fafb;
  --border-color: #e5e7eb;
  --gradient-bg: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --card-shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --border-radius: 12px;
}

body {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 50%, #f0f9ff 100%);
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  color: var(--text-color);
  line-height: 1.6;
}

.main-container {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  margin: 20px auto;
  max-width: 1200px;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.header-section {
  background: var(--gradient-bg);
  color: white;
  padding: 48px 32px;
  text-align: center;
}

.header-title {
  font-size: 2.75rem;
  font-weight: 800;
  margin-bottom: 16px;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
  letter-spacing: -0.025em;
}

.header-subtitle {
  font-size: 1.125rem;
  opacity: 0.95;
  font-weight: 400;
}

.content-section {
  padding: 40px 32px;
  background: #fafbfc;
}

.config-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.config-title {
  color: var(--dark-color);
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.config-title i {
  color: var(--primary-color);
}

.feature-badge {
  background: linear-gradient(135deg, var(--success-color) 0%, var(--accent-color) 100%);
  color: white;
  padding: 10px 16px;
  border-radius: 24px;
  font-size: 0.875rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 20px;
}

.config-url {
  background: #f8fafc;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
  font-family: 'SF Mono', 'Monaco', monospace;
  font-size: 0.875rem;
  word-break: break-all;
  margin-bottom: 20px;
  color: var(--dark-color);
  line-height: 1.5;
}

.copy-btn {
  background: var(--gradient-bg);
  border: none;
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.params-list {
  background: #f8fafc;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 24px;
  margin-top: 24px;
}

.params-list h6 {
  color: var(--dark-color);
  font-weight: 700;
  margin-bottom: 16px;
  font-size: 1.125rem;
}

.params-list li {
  padding: 10px 0;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: flex-start;
  gap: 12px;
  font-size: 0.9rem;
  color: var(--text-color);
}

.params-list li i {
  color: var(--primary-color);
  width: 16px;
  margin-top: 2px;
}

.params-list li strong {
  color: var(--dark-color);
  font-weight: 600;
}
</style>
</head>
<body>
<div class="main-container">
    <div class="header-section">
        <h1 class="header-title">
            <i class="fas fa-cloud"></i>
            VLESS 代理配置面板
        </h1>
        <p class="header-subtitle">Cloudflare Workers/Pages VLESS 代理脚本 V25.5.27</p>
    </div>
    
    <div class="content-section">
        <div class="config-card">
            <h3 class="config-title">
                <i class="fas fa-shield-alt"></i>
                CF-Workers VLESS+WS 节点
            </h3>
            <div class="feature-badge">
                <i class="fas fa-unlock me-1"></i>
                关闭TLS加密，无视域名阻断
            </div>
            <div class="config-url">vless://<EMAIL>:8880?encryption=none&security=none&type=ws&host=example.com&path=%2F%3Fed%3D2560#example.com</div>
            <button class="copy-btn">
                <i class="fas fa-copy"></i>
                复制配置链接
            </button>
            
            <div class="params-list">
                <h6><i class="fas fa-cog me-2"></i>客户端参数配置</h6>
                <ul style="list-style: none; padding: 0; margin: 0;">
                    <li><i class="fas fa-server"></i><strong>客户端地址：</strong>自定义域名 / 优选域名 / 优选IP / 反代IP</li>
                    <li><i class="fas fa-plug"></i><strong>端口：</strong>80, 8080, 8880, 2052, 2082, 2086, 2095</li>
                    <li><i class="fas fa-key"></i><strong>用户ID：</strong>test-uuid-12345</li>
                    <li><i class="fas fa-network-wired"></i><strong>传输协议：</strong>ws / websocket</li>
                    <li><i class="fas fa-globe"></i><strong>伪装域名：</strong>example.com</li>
                    <li><i class="fas fa-route"></i><strong>路径：</strong>/?ed=2560</li>
                    <li style="border-bottom: none;"><i class="fas fa-lock-open"></i><strong>传输安全：</strong>关闭</li>
                </ul>
            </div>
        </div>
    </div>
</div>
</body>
</html>
